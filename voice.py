#!/usr/bin/env python3
"""
CPU-efficient voice agent
STT: Moonshine tiny (ONNX, streaming)
TTS: ChatTTS (streaming)
LLM: Gemini 2.0 Flash
UI: FastRTC Gradio
"""

import os
import sys
from pathlib import Path

# Add the chattts directory to sys.path for correct module import
sys.path.insert(0, str(Path(__file__).parent / "chattts"))
import json
import subprocess
from pathlib import Path
from typing import Iterable

import numpy as np
import requests
from dotenv import load_dotenv
from fastrtc import Stream, ReplyOnPause
import moonshine_onnx as ms
import ChatTTS

# ---------- config ----------
class Config:
    load_dotenv()
    GEMINI_KEY: str = os.getenv("GEMINI_API_KEY", "YOUR_KEY")
    GEMINI_URL: str = (
        "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent"
        f"?key={GEMINI_KEY}"
    )

    TOOLS_PATH: Path = Path("tools.json")
    TOOLS: dict = {}
    if TOOLS_PATH.exists():
        TOOLS = json.loads(TOOLS_PATH.read_text())

# Access config values via Config.VAR_NAME
# Example: Config.GEMINI_KEY


# ---------- STT ----------
class MoonshineSTT:
    """Handles Speech-to-Text (STT) using the Moonshine ONNX model."""
    def __init__(self):
        """Initializes the MoonshineSTT with a tiny ONNX model."""
        self.model = ms.MoonshineOnnxModel(model_name="tiny")

    def __call__(self, audio: np.ndarray) -> str:
        """Transcribes audio input to text.

        Args:
            audio (np.ndarray): The input audio data, potentially a tuple (data, sample_rate) or a NumPy array.

        Returns:
            str: The transcribed text.
        """
        # Ensure audio is a 1D NumPy array of float32
        if isinstance(audio, tuple):
            # Assuming audio is (data, sample_rate) from fastrtc
            audio_data = audio[0]
        else:
            audio_data = audio

        if not isinstance(audio_data, np.ndarray):
            audio_data = np.array(audio_data, dtype=np.float32)

        # Flatten to 1D if it's multi-dimensional (e.g., stereo)
        # Reshape to [batch, samples] as expected by moonshine_onnx
        if audio_data.ndim == 1:
            audio_data = audio_data[np.newaxis, :]
        elif audio_data.ndim > 2:
            # If it's more than 2D, flatten and then reshape to [1, samples]
            audio_data = audio_data.flatten()[np.newaxis, :]

        return ms.transcribe(audio_data, self.model)[0].strip()

# ---------- TTS ----------
class ChatTTSStreamer:
    """Handles Text-to-Speech (TTS) streaming using the ChatTTS model."""
    def __init__(self):
        """Initializes the ChatTTSStreamer, loading the ChatTTS model locally."""
        self.chat = ChatTTS.Chat()
        self.chat.load(source="local", custom_path="./chattts")
        self.seed = 42  # deterministic voice

    def __call__(self, text: str) -> Iterable[np.ndarray]:
        """Generates audio from text input as an iterable of NumPy arrays.

        Args:
            text (str): The input text to be converted to speech.

        Yields:
            np.ndarray: Chunks of audio data as NumPy arrays.
        """
        wavs = self.chat.infer(
            text,
            use_decoder=True,
            params_infer_code={"spk_emb": self.chat.get_spk_emb(self.seed)},
            skip_refine_text=True,
        )
        for w in wavs:
            yield w

# ---------- LLM ----------
def ask_gemini(prompt: str) -> str:
    """Sends a prompt to the Gemini LLM and returns the generated text.

    Args:
        prompt (str): The text prompt to send to the LLM.

    Returns:
        str: The LLM's response or an error message.
    """
    payload = {"contents": [{"parts": [{"text": prompt}]}]}
    try:
        r = requests.post(Config.GEMINI_URL, json=payload, timeout=10)
        r.raise_for_status()
        return r.json()["candidates"][0]["content"]["parts"][0]["text"]
    except Exception as e:
        return f"LLM error: {e}"

# ---------- Tools ----------
def run_tool(name: str) -> str:
    """Executes a predefined tool command.

    Args:
        name (str): The name of the tool to execute.

    Returns:
        str: The output of the tool command or an error message.
    """
    cmd = Config.TOOLS[name]["command"]
    try:
        return subprocess.check_output(cmd, shell=True, text=True, timeout=8)
    except Exception as e:
        return f"Tool error: {e}"

# ---------- Agent ----------
stt = MoonshineSTT()
tts = ChatTTSStreamer()

def agent(audio: np.ndarray) -> Iterable[np.ndarray]:
    """Processes audio input, generates a prompt, and returns an iterable of audio responses.

    Args:
        audio (np.ndarray): The input audio data as a NumPy array.

    Yields:
        np.ndarray: Chunks of audio data as NumPy arrays.
    """
    prompt = stt(audio)
    print("🎤", prompt)

    # crude tool trigger
    tool_out = ""
    for t in Config.TOOLS:
        if t.lower() in prompt.lower():
            tool_out = run_tool(t)
            break

    full_prompt = f"{prompt}\n{tool_out}".strip()
    reply = ask_gemini(full_prompt)
    print("🤖", reply)

    for wav in tts(reply):
        yield wav

# ---------- UI ----------
if __name__ == "__main__":
    stream = Stream(
        ReplyOnPause(agent),
        modality="audio",
        mode="send-receive"
    )
    stream.ui.launch(server_name="0.0.0.0", server_port=7860)