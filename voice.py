#!/usr/bin/env python3
"""
CPU-efficient voice agent
STT: Moonshine tiny (ONNX, streaming)
TTS: ChatTTS (streaming)
LLM: Gemini 2.0 Flash
UI: FastRTC Gradio
"""

import os
import sys
from pathlib import Path

# Add the chattts directory to sys.path for correct module import
sys.path.insert(0, str(Path(__file__).parent / "chattts"))
import json
import subprocess
from pathlib import Path
from typing import Iterable

import numpy as np
import requests
from dotenv import load_dotenv
from fastrtc import Stream, ReplyOnPause
import moonshine_onnx as ms
import ChatTTS

# ---------- config ----------
class Config:
    load_dotenv()
    GEMINI_KEY: str = os.getenv("GEMINI_API_KEY", "YOUR_KEY")
    GEMINI_URL: str = (
        "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent"
        f"?key={GEMINI_KEY}"
    )

    TOOLS_PATH: Path = Path("tools.json")
    TOOLS: list = []
    if TOOLS_PATH.exists():
        TOOLS = json.loads(TOOLS_PATH.read_text())

# Access config values via Config.VAR_NAME
# Example: Config.GEMINI_KEY


# ---------- STT ----------
class MoonshineSTT:
    """Handles Speech-to-Text (STT) using the Moonshine ONNX model."""
    def __init__(self):
        """Initializes the MoonshineSTT with a tiny ONNX model."""
        self.model = ms.MoonshineOnnxModel(model_name="tiny")

    def __call__(self, audio: np.ndarray) -> str:
        """Transcribes audio input to text.

        Args:
            audio (np.ndarray): The input audio data, potentially a tuple (data, sample_rate) or a NumPy array.

        Returns:
            str: The transcribed text.
        """
        try:
            # Debug: Print the original audio format
            print(f"DEBUG: Original audio type: {type(audio)}")
            if hasattr(audio, 'shape'):
                print(f"DEBUG: Original audio shape: {audio.shape}")

            # FastRTC sends audio as a tuple: (sample_rate, audio_data)
            if isinstance(audio, tuple):
                print(f"DEBUG: Tuple length: {len(audio)}")
                for i, element in enumerate(audio):
                    print(f"DEBUG: Tuple element {i}: type={type(element)}, value={element}")

                # Extract audio data from tuple
                if len(audio) >= 2:
                    sample_rate, audio_data = audio[0], audio[1]
                    print(f"DEBUG: Extracted sample_rate={sample_rate}, audio_data type={type(audio_data)}")
                else:
                    print("WARNING: Unexpected tuple format, using first element")
                    audio_data = audio[0]
            else:
                audio_data = audio

            # Handle the case where audio_data is a scalar (single sample)
            if isinstance(audio_data, (int, float, np.integer, np.floating)):
                print(f"WARNING: Received scalar audio data: {audio_data}, skipping transcription")
                return ""

            # Convert to numpy array if needed
            if not isinstance(audio_data, np.ndarray):
                if isinstance(audio_data, list):
                    audio_data = np.array(audio_data, dtype=np.float32)
                else:
                    print(f"WARNING: Unexpected audio data type: {type(audio_data)}")
                    return ""

            print(f"DEBUG: Audio data shape: {audio_data.shape}, dtype: {audio_data.dtype}")

            # Check if we have enough audio data
            if audio_data.size < 1600:  # Less than 0.1 seconds at 16kHz
                print(f"WARNING: Audio too short ({audio_data.size} samples), need at least 1600 samples (0.1s)")
                return ""

            # Ensure float32 dtype
            if audio_data.dtype != np.float32:
                if audio_data.dtype == np.int16:
                    # Convert int16 to float32 (normalize from -32768,32767 to -1,1)
                    audio_data = audio_data.astype(np.float32) / 32768.0
                else:
                    audio_data = audio_data.astype(np.float32)
                print(f"DEBUG: Converted to float32, shape: {audio_data.shape}")

            # Handle different dimensionalities - moonshine expects [batch, samples]
            if audio_data.ndim == 1:
                # 1D array: reshape to [1, samples] as expected by moonshine
                audio_data = audio_data[np.newaxis, :]
                print(f"DEBUG: Reshaped 1D to [batch, samples]: {audio_data.shape}")
            elif audio_data.ndim == 2:
                # 2D array: check if it's already [batch, samples] or needs conversion
                if audio_data.shape[0] > audio_data.shape[1]:
                    # Likely [samples, channels], transpose and take first channel
                    audio_data = audio_data[:, 0][np.newaxis, :]
                    print(f"DEBUG: Converted [samples, channels] to [batch, samples]: {audio_data.shape}")
                else:
                    # Likely [channels, samples] or [batch, samples]
                    if audio_data.shape[0] > 1:
                        # Multiple channels, take first channel
                        audio_data = audio_data[0:1, :]
                        print(f"DEBUG: Took first channel: {audio_data.shape}")
            else:
                print(f"WARNING: Unexpected audio dimensionality: {audio_data.ndim}D")
                return ""

            print(f"DEBUG: Final audio shape: {audio_data.shape}")

            # Validate audio meets moonshine requirements
            if audio_data.ndim != 2:
                print(f"ERROR: Audio should be 2D [batch, samples], got {audio_data.ndim}D")
                return ""

            # Check duration (moonshine expects 0.1s to 64s at 16kHz)
            duration = audio_data.shape[1] / 16000.0
            if duration < 0.1:
                print(f"WARNING: Audio too short: {duration:.3f}s, need at least 0.1s")
                return ""
            elif duration > 64.0:
                print(f"WARNING: Audio too long: {duration:.3f}s, truncating to 64s")
                max_samples = int(64 * 16000)
                audio_data = audio_data[:, :max_samples]

            print(f"DEBUG: Audio duration: {duration:.3f}s, shape: {audio_data.shape}")

            # Transcribe using moonshine
            result = ms.transcribe(audio_data, self.model)[0].strip()
            print(f"DEBUG: Transcription result: '{result}'")
            return result

        except Exception as e:
            print(f"ERROR in STT: {e}")
            print(f"ERROR: Audio type: {type(audio)}")
            if hasattr(audio, 'shape'):
                print(f"ERROR: Audio shape: {audio.shape}")
            return f"STT Error: {str(e)}"

# ---------- TTS ----------
class ChatTTSStreamer:
    """Handles Text-to-Speech (TTS) streaming using the ChatTTS model."""
    def __init__(self):
        """Initializes the ChatTTSStreamer, loading the ChatTTS model locally."""
        self.chat = ChatTTS.Chat()
        self.chat.load(source="local", custom_path="./chattts")
        self.seed = 42  # deterministic voice

    def __call__(self, text: str) -> Iterable[np.ndarray]:
        """Generates audio from text input as an iterable of NumPy arrays.

        Args:
            text (str): The input text to be converted to speech.

        Yields:
            np.ndarray: Chunks of audio data as NumPy arrays.
        """
        wavs = self.chat.infer(
            text,
            use_decoder=True,
            params_infer_code={"spk_emb": self.chat.get_spk_emb(self.seed)},
            skip_refine_text=True,
        )
        for w in wavs:
            yield w

# ---------- LLM ----------
def ask_gemini(prompt: str) -> str:
    """Sends a prompt to the Gemini LLM and returns the generated text.

    Args:
        prompt (str): The text prompt to send to the LLM.

    Returns:
        str: The LLM's response or an error message.
    """
    payload = {"contents": [{"parts": [{"text": prompt}]}]}
    try:
        r = requests.post(Config.GEMINI_URL, json=payload, timeout=10)
        r.raise_for_status()
        return r.json()["candidates"][0]["content"]["parts"][0]["text"]
    except Exception as e:
        return f"LLM error: {e}"

# ---------- Tools ----------
def run_tool(tool_config: dict) -> str:
    """Executes a predefined tool command.

    Args:
        tool_config (dict): The tool configuration dictionary.

    Returns:
        str: The output of the tool command or an error message.
    """
    try:
        if "shell" in tool_config:
            # Execute shell command
            cmd = tool_config["shell"]
            return subprocess.check_output(cmd, shell=True, text=True, timeout=8)
        elif "url" in tool_config:
            # Open URL (for now just return a message)
            return f"Opening URL: {tool_config['url']}"
        elif "app" in tool_config:
            # Launch application
            app = tool_config["app"]
            subprocess.Popen(app, shell=True)
            return f"Launched application: {app}"
        else:
            return "Tool configuration not supported"
    except Exception as e:
        return f"Tool error: {e}"

# ---------- Agent ----------
stt = MoonshineSTT()
tts = ChatTTSStreamer()

def agent(audio: np.ndarray) -> Iterable[np.ndarray]:
    """Processes audio input, generates a prompt, and returns an iterable of audio responses.

    Args:
        audio (np.ndarray): The input audio data as a NumPy array.

    Yields:
        np.ndarray: Chunks of audio data as NumPy arrays.
    """
    prompt = stt(audio)
    print("🎤", prompt)

    # crude tool trigger
    tool_out = ""
    user_input = prompt.lower() if isinstance(prompt, str) else ""

    # Skip tool processing if prompt is not a string
    if not isinstance(prompt, str):
        print(f"WARNING: Prompt is not a string, skipping tool processing. Type: {type(prompt)}")
    else:
        for tool in Config.TOOLS:
            if "phrase" in tool and tool["phrase"].lower() in user_input:
                print(f"DEBUG: Tool triggered: {tool['phrase']}")
                tool_out = run_tool(tool)
                break

    full_prompt = f"{prompt}\n{tool_out}".strip()
    reply = ask_gemini(full_prompt)
    print("🤖", reply)

    for wav in tts(reply):
        yield wav

# ---------- UI ----------
if __name__ == "__main__":
    stream = Stream(
        ReplyOnPause(agent),
        modality="audio",
        mode="send-receive"
    )
    stream.ui.launch(server_name="0.0.0.0", server_port=7860)