name: Code formatter for PRs
on: pull_request
jobs:
  format-code:
    runs-on: ubuntu-latest
    permissions:
      # Give the default GITHUB_TOKEN write permission to commit and push the
      # added or changed files to the repository.
      contents: write
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.head_ref }}
      - name: Install Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install ruff
      # Update output format to enable automatic inline annotations.
      - name: Run Ruff
        run: ruff format
      - name: Commit formatting changes
        uses: stefanzweifel/git-auto-commit-action@v5
        with:
          commit_message: Apply code formatting
          branch: ${{ github.head_ref }}
