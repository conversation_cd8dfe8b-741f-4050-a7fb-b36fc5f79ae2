{"name": "moonshine-web", "version": "1.0.0", "description": "A demo of the Moonshine ASR model running on-device, in the browser.", "type": "module", "scripts": {"dev": "vite", "clean": "rm -rf node_modules/ dist/", "build": "vite build", "get-models": "node downloader.js", "preview": "vite preview"}, "author": "<PERSON>, Useful Sensors", "license": "MIT", "dependencies": {"llama-tokenizer-js": "^1.2.2", "onnxruntime-web": "^1.20.0"}, "devDependencies": {"@huggingface/hub": "^0.19.0", "vite": "^5.4.11"}}