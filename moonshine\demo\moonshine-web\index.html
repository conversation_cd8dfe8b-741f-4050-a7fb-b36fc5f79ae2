<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.png" />
    <link rel="stylesheet" href="/index.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Moonshine – lightweight ASR by Useful Sensors</title>
  </head>
  <body>
    <div id="root"> 
      <div class="container">
        <div class="logo-container">
          <svg version="1.1" id="logo" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 800 800" >
            <path class="st0" d="M409,760C205.6,760,40,594.4,40,390.9C40,228.5,144.4,88.5,294.8,40C236.6,100.9,203,182.9,203,268.3
                c0,181.7,147.3,329.5,328.3,329.5c85.8,0,168.1-34.2,228.8-93.4C711.9,655.3,571.8,760,409,760L409,760z"/>
            <line class="st1" id="l1" x1="310.1" y1="293.8" x2="310.1" y2="325.8"/>
            <line class="st1" id="l2" x1="729.8" y1="293.8" x2="729.8" y2="325.8"/>
            <line class="st1" id="l3" x1="370" y1="220" x2="370" y2="399.6"/>
            <line class="st1" id="l4" x1="430" y1="245.9" x2="430" y2="373.7"/>
            <line class="st1" id="l5" x1="489.9" y1="293.8" x2="489.9" y2="325.8"/>
            <line class="st1" id="l6" x1="548.1" y1="278.2" x2="548.1" y2="342.1"/>
            <line class="st1" id="l7" x1="609.9" y1="220.4" x2="609.9" y2="400"/>
            <line class="st1" id="l8" x1="669.8" y1="245.9" x2="669.8" y2="373.7"/>
          </svg>
          <h1>Moonshine</h1>
          fast, accurate, and lightweight speech-to-text models running in your browser
        </div>
        <div class="justify-center">
          <select name="models" id="models">
            <option value="tiny" selected>moonshine/tiny</option>
            <option value="base">moonshine/base</option>
          </select>          
          <input type="file" id="upload" style="display: none;" />
          <button id="browse" onclick="document.getElementById('upload').click();">Browse</button>
          <button id="startRecord">Record</button>
          <button id="stopRecord" style="display: none;">Stop</button>
        </div>
        <div id="audioPanel">
          <div class="justify-center">
            <audio id="sound" type="audio/wav" controls></audio>
          </div>
          <div class="justify-center">
            <button id="transcribe">Transcribe</button>
          </div>
        </div>
        <div class="justify-center">
          <span id="transcription"></span>
        </div>
      </div>
    </div>
    <script type="module" src="/src/index.js"></script>
  </body>
</html>
