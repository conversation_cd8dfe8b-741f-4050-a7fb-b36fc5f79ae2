name: Unit Test
on: [ push, pull_request ]
jobs:
  build:
    runs-on: ${{ matrix.os }}

    if: "!contains(github.event.head_commit.message, 'chore(format): ') && !contains(github.event.head_commit.message, 'chore(env): ')"

    strategy:
      matrix:
        python-version: ["3.8", "3.9", "3.10"]
        os: [ubuntu-latest]
      fail-fast: true

    steps:

      - uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install Dependents
        run: |
          sudo apt-get install -y portaudio19-dev python3-pyaudio

      - name: Create venv
        run: python3 -m venv .venv

      - name: Activate venv
        run: |
          . .venv/bin/activate
          echo PATH=$PATH >> $GITHUB_ENV

      - name: Test Install
        run: pip install .

      - name: Install Dependencies
        run: pip install -r requirements.txt

      - name: Run Test
        run: tests/testall.sh
