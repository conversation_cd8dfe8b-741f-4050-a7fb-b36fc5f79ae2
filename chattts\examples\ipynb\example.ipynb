{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Import packages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os, sys\n", "\n", "if sys.platform == \"darwin\":\n", "    os.environ[\"PYTORCH_ENABLE_MPS_FALLBACK\"] = \"1\"\n", "\n", "if not \"root_dir\" in globals():\n", "    now_dir = os.getcwd()  # skip examples/ipynb\n", "    root_dir = os.path.join(now_dir, \"../../\")\n", "    sys.path.append(root_dir)\n", "    print(\"init root dir to\", root_dir)\n", "\n", "import torch\n", "\n", "torch._dynamo.config.cache_size_limit = 64\n", "torch._dynamo.config.suppress_errors = True\n", "torch.set_float32_matmul_precision(\"high\")\n", "\n", "import ChatTTS\n", "from tools.logger import get_logger\n", "from tools.normalizer import normalizer_en_nemo_text, normalizer_zh_tn\n", "from IPython.display import Audio"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Models"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["os.chdir(root_dir)\n", "\n", "logger = get_logger(\"ChatTTS\")\n", "chat = ChatTTS.Chat(logger)\n", "\n", "# try to load normalizer\n", "try:\n", "    chat.normalizer.register(\"en\", normalizer_en_nemo_text())\n", "except ValueError as e:\n", "    logger.error(e)\n", "except:\n", "    logger.warning(\"Package nemo_text_processing not found!\")\n", "    logger.warning(\n", "        \"Run: conda install -c conda-forge pynini=2.1.5 && pip install nemo_text_processing\",\n", "    )\n", "try:\n", "    chat.normalizer.register(\"zh\", normalizer_zh_tn())\n", "except ValueError as e:\n", "    logger.error(e)\n", "except:\n", "    logger.warning(\"Package WeTextProcessing not found!\")\n", "    logger.warning(\n", "        \"Run: conda install -c conda-forge pynini=2.1.5 && pip install WeTextProcessing\",\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Here are three choices for loading models,"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 1. Load models from Hugging Face (not suitable in CN)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# use force_redownload=True if the weights have been updated.\n", "chat.load(source=\"huggingface\", force_redownload=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2. Load models from local directories 'asset' and 'config' (recommend)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["chat.load()\n", "# chat.load(source='local') same as above"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3. Load models from a custom path"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# write the model path into custom_path\n", "chat.load(source=\"custom\", custom_path=\"YOUR CUSTOM PATH\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### You can also unload models to save the memory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["chat.unload()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Inference"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON> infer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["texts = [\n", "    \"So we found being competitive and collaborative was a huge way of staying motivated towards our goals, so one person to call when you fall off, one person who gets you back on then one person to actually do the activity with.\",\n", "] * 3 + [\n", "    \"我觉得像我们这些写程序的人，他，我觉得多多少少可能会对开源有一种情怀在吧我觉得开源是一个很好的形式。现在其实最先进的技术掌握在一些公司的手里的话，就他们并不会轻易的开放给所有的人用。\"\n", "] * 3\n", "\n", "wavs = chat.infer(texts)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Audio(wavs[0], rate=24_000, autoplay=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Audio(wavs[3], rate=24_000, autoplay=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Custom params"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["params_infer_code = ChatTTS.Chat.InferCodeParams(\n", "    prompt=\"[speed_5]\",\n", "    temperature=0.3,\n", ")\n", "params_refine_text = ChatTTS.Chat.RefineTextParams(\n", "    prompt=\"[oral_2][laugh_0][break_6]\",\n", ")\n", "\n", "wav = chat.infer(\n", "    \"四川美食可多了，有麻辣火锅、宫保鸡丁、麻婆豆腐、担担面、回锅肉、夫妻肺片等，每样都让人垂涎三尺。\",\n", "    params_refine_text=params_refine_text,\n", "    params_infer_code=params_infer_code,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Audio(wav[0], rate=24_000, autoplay=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Fix random speaker"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rand_spk = chat.sample_random_speaker()\n", "print(rand_spk)  # save it for later timbre recovery\n", "\n", "params_infer_code = ChatTTS.Chat.InferCodeParams(\n", "    spk_emb=rand_spk,\n", ")\n", "\n", "wav = chat.infer(\n", "    \"四川美食确实以辣闻名，但也有不辣的选择。比如甜水面、赖汤圆、蛋烘糕、叶儿粑等，这些小吃口味温和，甜而不腻，也很受欢迎。\",\n", "    params_infer_code=params_infer_code,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Audio(wav[0], rate=24_000, autoplay=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Zero shot (simulate speaker)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from tools.audio import load_audio\n", "\n", "spk_smp = chat.sample_audio_speaker(load_audio(\"sample.mp3\", 24000))\n", "print(spk_smp)  # save it in order to load the speaker without sample audio next time\n", "\n", "params_infer_code = ChatTTS.Chat.InferCodeParams(\n", "    spk_smp=spk_smp,\n", "    txt_smp=\"与sample.mp3内容完全一致的文本转写。\",\n", ")\n", "\n", "wav = chat.infer(\n", "    \"四川美食确实以辣闻名，但也有不辣的选择。比如甜水面、赖汤圆、蛋烘糕、叶儿粑等，这些小吃口味温和，甜而不腻，也很受欢迎。\",\n", "    params_infer_code=params_infer_code,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Audio(wav[0], rate=24_000, autoplay=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Two stage control"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text = \"So we found being competitive and collaborative was a huge way of staying motivated towards our goals, so one person to call when you fall off, one person who gets you back on then one person to actually do the activity with.\"\n", "refined_text = chat.infer(text, refine_text_only=True)\n", "refined_text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["wav = chat.infer(refined_text, skip_refine_text=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Audio(wav[0], rate=24_000, autoplay=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## LLM Call"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from tools.llm import ChatOpenAI\n", "\n", "API_KEY = \"\"\n", "client = ChatOpenAI(\n", "    api_key=API_KEY, base_url=\"https://api.deepseek.com\", model=\"deepseek-chat\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["user_question = \"四川有哪些好吃的美食呢?\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text = client.call(user_question, prompt_version=\"deepseek\")\n", "text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text = client.call(text, prompt_version=\"deepseek_TN\")\n", "text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["wav = chat.infer(text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Audio(wav[0], rate=24_000, autoplay=True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 4}