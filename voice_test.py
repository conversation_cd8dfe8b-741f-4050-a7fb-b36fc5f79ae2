#!/usr/bin/env python3
"""
Test version of voice agent to debug audio issues
"""

import os
import json
import subprocess
from pathlib import Path
from typing import Iterable

import numpy as np
import requests
from dotenv import load_dotenv
from fastrtc import Stream, ReplyOnPause
import moonshine_onnx as ms

# ---------- config ----------
class Config:
    load_dotenv()
    GEMINI_KEY: str = os.getenv("GEMINI_API_KEY", "YOUR_KEY")
    GEMINI_URL: str = (
        "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent"
        f"?key={GEMINI_KEY}"
    )

    TOOLS_PATH: Path = Path("tools.json")
    TOOLS: list = []
    if TOOLS_PATH.exists():
        TOOLS = json.loads(TOOLS_PATH.read_text())

# ---------- STT ----------
class MoonshineSTT:
    """Handles Speech-to-Text (STT) using the Moonshine ONNX model."""
    def __init__(self):
        """Initializes the MoonshineSTT with a tiny ONNX model."""
        self.model = ms.MoonshineOnnxModel(model_name="tiny")

    def __call__(self, audio: np.ndarray) -> str:
        """Transcribes audio input to text."""
        try:
            # Debug: Print the original audio format
            print(f"DEBUG: Original audio type: {type(audio)}")
            if hasattr(audio, 'shape'):
                print(f"DEBUG: Original audio shape: {audio.shape}")
            
            # Handle different input formats
            if isinstance(audio, tuple):
                # FastRTC might send audio in different tuple formats
                print(f"DEBUG: Tuple length: {len(audio)}")
                
                audio_data = None
                # Check each element of the tuple
                for i, element in enumerate(audio):
                    print(f"DEBUG: Tuple element {i} type: {type(element)}, value: {element}")
                    if hasattr(element, 'shape') and isinstance(element, np.ndarray) and element.size > 1:
                        # This looks like audio data (numpy array with multiple samples)
                        audio_data = element
                        print(f"DEBUG: Found audio data in tuple element {i}, shape: {element.shape}")
                        break
                    elif isinstance(element, list) and len(element) > 1:
                        # This might be audio data as a list
                        audio_data = np.array(element, dtype=np.float32)
                        print(f"DEBUG: Converted list to numpy array, shape: {audio_data.shape}")
                        break
                    elif isinstance(element, np.ndarray) and element.size == 1:
                        # Single sample - might be part of streaming
                        print(f"DEBUG: Found single sample in element {i}")
                        continue
                
                # If no suitable element found, create dummy audio for testing
                if audio_data is None:
                    print("WARNING: Could not identify audio data in tuple, creating dummy audio for testing")
                    # Create a small dummy audio array for testing
                    audio_data = np.zeros(1024, dtype=np.float32)
                    
                print(f"DEBUG: Final audio_data type: {type(audio_data)}")
            else:
                audio_data = audio
            
            # Check for None or empty audio
            if audio_data is None:
                print("WARNING: Received None audio data")
                return ""
            
            # Convert to numpy array if needed
            if not isinstance(audio_data, np.ndarray):
                audio_data = np.array(audio_data, dtype=np.float32)
                print(f"DEBUG: Converted to numpy array, shape: {audio_data.shape}")
            
            # Check if array is empty
            if audio_data.size == 0:
                print("WARNING: Received empty audio data")
                return ""
            
            # Ensure float32 dtype
            if audio_data.dtype != np.float32:
                audio_data = audio_data.astype(np.float32)
                print(f"DEBUG: Converted to float32, shape: {audio_data.shape}")
            
            # Handle different dimensionalities and convert to [batch, samples] format
            print(f"DEBUG: Audio data shape before processing: {audio_data.shape}")
            
            if audio_data.ndim == 1:
                # 1D array: reshape to [1, samples]
                audio_data = audio_data[np.newaxis, :]
                print(f"DEBUG: Reshaped 1D to 2D: {audio_data.shape}")
            elif audio_data.ndim == 2:
                # 2D array: check if it's [samples, channels] or [channels, samples]
                if audio_data.shape[0] > audio_data.shape[1]:
                    # Likely [samples, channels], transpose to [channels, samples]
                    audio_data = audio_data.T
                    print(f"DEBUG: Transposed 2D array: {audio_data.shape}")
                
                # If multiple channels, take the first channel or average them
                if audio_data.shape[0] > 1:
                    audio_data = np.mean(audio_data, axis=0, keepdims=True)
                    print(f"DEBUG: Averaged channels: {audio_data.shape}")
            elif audio_data.ndim > 2:
                # More than 2D: flatten and reshape to [1, samples]
                audio_data = audio_data.flatten()[np.newaxis, :]
                print(f"DEBUG: Flattened >2D array: {audio_data.shape}")
            else:
                # 0D array (scalar): convert to [1, 1]
                audio_data = np.array([[audio_data]], dtype=np.float32)
                print(f"DEBUG: Converted scalar to 2D: {audio_data.shape}")
            
            # Final validation
            if audio_data.ndim != 2:
                raise ValueError(f"Audio data should be 2D after processing, got {audio_data.ndim}D")
            
            print(f"DEBUG: Final audio shape before transcription: {audio_data.shape}")
            
            # Transcribe the audio
            result = ms.transcribe(audio_data, self.model)[0].strip()
            print(f"DEBUG: Transcription result: '{result}'")
            return result
            
        except Exception as e:
            print(f"ERROR in STT: {e}")
            print(f"ERROR: Audio type: {type(audio)}")
            if hasattr(audio, 'shape'):
                print(f"ERROR: Audio shape: {audio.shape}")
            return f"STT Error: {str(e)}"

# ---------- TTS ----------
class DummyTTSStreamer:
    """Dummy TTS for testing."""
    def __init__(self):
        print("DEBUG: Using dummy TTS for testing")

    def __call__(self, text: str) -> Iterable[np.ndarray]:
        """Generates dummy audio from text input."""
        print(f"DEBUG: TTS would say: {text}")
        # Generate a simple sine wave as dummy audio
        sample_rate = 24000
        duration = 1.0  # 1 second
        frequency = 440  # A4 note
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        wave = np.sin(frequency * 2 * np.pi * t).astype(np.float32)
        
        # Yield the audio in chunks
        chunk_size = 1024
        for i in range(0, len(wave), chunk_size):
            yield wave[i:i+chunk_size]

# ---------- LLM ----------
def ask_gemini(prompt: str) -> str:
    """Sends a prompt to the Gemini LLM and returns the generated text."""
    payload = {"contents": [{"parts": [{"text": prompt}]}]}
    try:
        r = requests.post(Config.GEMINI_URL, json=payload, timeout=10)
        r.raise_for_status()
        return r.json()["candidates"][0]["content"]["parts"][0]["text"]
    except Exception as e:
        return f"LLM error: {e}"

# ---------- Tools ----------
def run_tool(tool_config: dict) -> str:
    """Executes a predefined tool command."""
    try:
        if "shell" in tool_config:
            # Execute shell command
            cmd = tool_config["shell"]
            return subprocess.check_output(cmd, shell=True, text=True, timeout=8)
        elif "url" in tool_config:
            # Open URL (for now just return a message)
            return f"Opening URL: {tool_config['url']}"
        elif "app" in tool_config:
            # Launch application
            app = tool_config["app"]
            subprocess.Popen(app, shell=True)
            return f"Launched application: {app}"
        else:
            return "Tool configuration not supported"
    except Exception as e:
        return f"Tool error: {e}"

# ---------- Agent ----------
stt = MoonshineSTT()
tts = DummyTTSStreamer()

def agent(audio: np.ndarray) -> Iterable[np.ndarray]:
    """Processes audio input, generates a prompt, and returns an iterable of audio responses."""
    prompt = stt(audio)
    print("🎤", prompt)

    # crude tool trigger
    tool_out = ""
    user_input = prompt.lower() if isinstance(prompt, str) else ""
    
    # Skip tool processing if prompt is not a string
    if not isinstance(prompt, str):
        print(f"WARNING: Prompt is not a string, skipping tool processing. Type: {type(prompt)}")
    else:
        for tool in Config.TOOLS:
            if "phrase" in tool and tool["phrase"].lower() in user_input:
                print(f"DEBUG: Tool triggered: {tool['phrase']}")
                tool_out = run_tool(tool)
                break

    full_prompt = f"{prompt}\n{tool_out}".strip()
    reply = ask_gemini(full_prompt)
    print("🤖", reply)

    for wav in tts(reply):
        yield wav

# ---------- UI ----------
if __name__ == "__main__":
    stream = Stream(
        ReplyOnPause(agent),
        modality="audio",
        mode="send-receive"
    )
    stream.ui.launch(server_name="0.0.0.0", server_port=7861)
