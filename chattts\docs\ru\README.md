# ChatTTS
> [!NOTE]
> Следующая информация может быть не самой последней, пожалуйста, смотрите английскую версию для актуальных данных.

[![Huggingface](https://img.shields.io/badge/🤗%20-Models-yellow.svg?style=for-the-badge)](https://huggingface.co/2Noise/ChatTTS)

[**English**](../../README.md) | [**简体中文**](../cn/README.md) | [**日本語**](../jp/README.md) | **Русский** | [**Español**](../es/README.md) | [**Français**](../fr/README.md) | [**한국어**](../kr/README.md)

ChatTTS - это модель преобразования текста в речь, специально разработанная для диалоговых сценариев, таких как помощник LLM. Она поддерживает как английский, так и китайский языки. Наша модель обучена на более чем 100 000 часах английского и китайского языков. Открытая версия на **[HuggingFace](https://huggingface.co/2Noise/ChatTTS)** - это предварительно обученная модель с 40 000 часами без SFT.

Для официальных запросов о модели и плане развития, пожалуйста, свяжитесь с нами по адресу **<EMAIL>**. Вы можете присоединиться к нашей группе QQ: 808364215 для обсуждения. Добавление вопросов на GitHub также приветствуется.

---
## Особенности
1. **Диалоговый TTS**: ChatTTS оптимизирован для задач, основанных на диалогах, что позволяет создавать натуральную и выразительную речь. Он поддерживает несколько говорящих, облегчая интерактивные беседы.
2. **Тонкий контроль**: Модель может предсказывать и контролировать тонкие просодические особенности, включая смех, паузы и вставные слова.
3. **Лучшая просодия**: ChatTTS превосходит большинство открытых моделей TTS с точки зрения просодии. Мы предоставляем предварительно обученные модели для поддержки дальнейших исследований и разработок.

Для подробного описания модели вы можете обратиться к **[видео на Bilibili](https://www.bilibili.com/video/BV1zn4y1o7iV)**

---

## Отказ от ответственности

Этот репозиторий предназначен только для академических целей. Он предназначен для образовательного и исследовательского использования и не должен использоваться в коммерческих или юридических целях. Авторы не гарантируют точность, полноту или надежность информации. Информация и данные, использованные в этом репозитории, предназначены только для академических и исследовательских целей. Данные получены из общедоступных источников, и авторы не заявляют о каких-либо правах собственности или авторских правах на данные.

ChatTTS - мощная система преобразования текста в речь. Однако очень важно использовать эту технологию ответственно и этично. Чтобы ограничить использование ChatTTS, мы добавили небольшое количество высокочастотного шума во время обучения модели на 40 000 часов и сжали качество аудио как можно больше с помощью формата MP3, чтобы предотвратить возможное использование злоумышленниками в преступных целях. В то же время мы внутренне обучили модель обнаружения и планируем открыть ее в будущем.

---
## Использование

<h4>Базовое использование</h4>

```python
import ChatTTS
from IPython.display import Audio
import torch

chat = ChatTTS.Chat()
chat.load(compile=False) # Установите значение True для лучшей производительности

texts = ["ВВЕДИТЕ ВАШ ТЕКСТ ЗДЕСЬ",]

wavs = chat.infer(texts)

torchaudio.save("output1.wav", torch.from_numpy(wavs[0]), 24000)
```

<h4>Продвинутое использование</h4>

```python
###################################
# Выборка говорящего из Гауссиана.

rand_spk = chat.sample_random_speaker()
print(rand_spk) # save it for later timbre recovery

params_infer_code = {
  'spk_emb': rand_spk, # добавить выбранного говорящего
  'temperature': .3, # использовать пользовательскую температуру
  'top_P': 0.7, # декодирование top P
  'top_K': 20, # декодирование top K
}

###################################
# Для контроля на уровне предложений.

# используйте oral_(0-9), laugh_(0-2), break_(0-7)
# для генерации специального токена в тексте для синтеза.
params_refine_text = {
  'prompt': '[oral_2][laugh_0][break_6]'
} 

wav = chat.infer(texts, params_refine_text=params_refine_text, params_infer_code=params_infer_code)

###################################
# Для контроля на уровне слов.
text = 'Какая ваша любимая английская еда?[uv_break]your favorite english food?[laugh][lbreak]'
wav = chat.infer(text, skip_refine_text=True, params_refine_text=params_refine_text,  params_infer_code=params_infer_code)
torchaudio.save("output2.wav", torch.from_numpy(wavs[0]), 24000)
```

<details open>
  <summary><h4>Пример: самопрезентация</h4></summary>

```python
inputs_ru = """
ChatTTS - это модель преобразования текста в речь, разработанная для диалоговых приложений. 
[uv_break]Она поддерживает смешанный языковой ввод [uv_break]и предлагает возможности множественных говорящих 
с точным контролем над просодическими элементами [laugh]как [uv_break]смех[laugh], [uv_break]паузы, [uv_break]и интонацию. 
[uv_break]Она обеспечивает натуральную и выразительную речь,[uv_break]поэтому, пожалуйста,
[uv_break] используйте проект ответственно и на свой страх и риск.[uv_break]
""".replace('\n', '') # Русский язык все еще находится в экспериментальной стадии.

params_refine_text = {
  'prompt': '[oral_2][laugh_0][break_4]'
} 
audio_array_ru = chat.infer(inputs_ru, params_refine_text=params_refine_text)
torchaudio.save("output3.wav", torch.from_numpy(audio_array_ru[0]), 24000)
```
[мужской говорящий](https://github.com/2noise/ChatTTS/assets/130631963/e0f51251-db7f-4d39-a0e9-3e095bb65de1)

[женский говорящий](https://github.com/2noise/ChatTTS/assets/130631963/f5dcdd01-1091-47c5-8241-c4f6aaaa8bbd)
</details>

---
## План развития
- [x] Открыть исходный код базовой модели на 40 тысяч часов и файла spk_stats
- [ ] Открыть исходный код кодировщика VQ и кода обучения Lora
- [ ] Потоковая генерация аудио без уточнения текста*
- [ ] Открыть исходный код версии на 40 тысяч часов с управлением множественными эмоциями
- [ ] ChatTTS.cpp возможно? (PR или новый репозиторий приветствуются.)

----
## Часто задаваемые вопросы

##### Сколько VRAM мне нужно? Как насчет скорости инференса?
Для 30-секундного аудиоклипа требуется как минимум 4 ГБ памяти GPU. Для GPU 4090, он может генерировать аудио, соответствующее примерно 7 семантическим токенам в секунду. Фактор реального времени (RTF) составляет около 0.3.

##### Стабильность модели кажется недостаточно хорошей, возникают проблемы с множественными говорящими или плохим качеством аудио.

Это проблема, которая обычно возникает с авторегрессивными моделями (для bark и valle). Это обычно трудно избежать. Можно попробовать несколько образцов, чтобы найти подходящий результат.

##### Помимо смеха, можем ли мы контролировать что-то еще? Можем ли мы контролировать другие эмоции?

В текущей выпущенной модели единственными элементами управления на уровне токенов являются [laugh], [uv_break] и [lbreak]. В будущих версиях мы можем открыть модели с дополнительными возможностями контроля эмоций.

---
## Благодарности
- [bark](https://github.com/suno-ai/bark), [XTTSv2](https://github.com/coqui-ai/TTS) и [valle](https://arxiv.org/abs/2301.02111) демонстрируют замечательный результат TTS с помощью системы авторегрессивного стиля.
- [fish-speech](https://github.com/fishaudio/fish-speech) показывает возможности GVQ как аудио токенизатора для моделирования LLM.
- [vocos](https://github.com/gemelo-ai/vocos), который используется в качестве предварительно обученного вокодера.

---
## Особая благодарность
- [wlu-audio lab](https://audio.westlake.edu.cn/) за ранние эксперименты с алгоритмами.
