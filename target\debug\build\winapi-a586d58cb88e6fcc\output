cargo:rerun-if-changed=build.rs
cargo:rerun-if-env-changed=WINAPI_NO_BUNDLED_LIBRARIES
cargo:rerun-if-env-changed=WINAPI_STATIC_NOBUNDLE
cargo:rustc-cfg=feature="subauth"
cargo:rustc-cfg=feature="ntdef"
cargo:rustc-cfg=feature="ktmtypes"
cargo:rustc-cfg=feature="inaddr"
cargo:rustc-cfg=feature="excpt"
cargo:rustc-cfg=feature="rpcndr"
cargo:rustc-cfg=feature="winnt"
cargo:rustc-cfg=feature="windef"
cargo:rustc-cfg=feature="wincred"
cargo:rustc-cfg=feature="sspi"
cargo:rustc-cfg=feature="minwindef"
cargo:rustc-cfg=feature="vcruntime"
cargo:rustc-cfg=feature="in6addr"
cargo:rustc-cfg=feature="lsalookup"
cargo:rustc-link-lib=dylib=advapi32
cargo:rustc-link-lib=dylib=credui
cargo:rustc-link-lib=dylib=kernel32
cargo:rustc-link-lib=dylib=secur32
